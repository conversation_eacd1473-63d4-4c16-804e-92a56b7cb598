{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>rk<PERSON> } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  const navItems = [\n    { name: \"首页\", href: \"/\" },\n    { name: \"产品\", href: \"/products\" },\n    { name: \"解决方案\", href: \"/solutions\" },\n    { name: \"关于我们\", href: \"/about\" },\n    { name: \"联系我们\", href: \"/contact\" },\n  ]\n\n  return (\n    <header className=\"fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Brain className=\"h-8 w-8 text-blue-600\" />\n              <Sparkles className=\"h-4 w-4 text-purple-500 absolute -top-1 -right-1\" />\n            </div>\n            <span className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              AI智能\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\">\n              登录\n            </Button>\n            <Button size=\"sm\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\">\n              免费试用\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"md:hidden py-4 border-t border-gray-200\"\n          >\n            <nav className=\"flex flex-col space-y-4\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium px-2 py-1\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"flex flex-col space-y-2 pt-4\">\n                <Button variant=\"outline\" size=\"sm\">\n                  登录\n                </Button>\n                <Button size=\"sm\" className=\"bg-gradient-to-r from-blue-600 to-purple-600\">\n                  免费试用\n                </Button>\n              </div>\n            </nav>\n          </motion.div>\n        )}\n      </div>\n    </header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,MAAM;YAAM,MAAM;QAAI;QACxB;YAAE,MAAM;YAAM,MAAM;QAAY;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAa;QACnC;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAA<PERSON>,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC;oCAAK,WAAU;8CAA+F;;;;;;;;;;;;sCAMjH,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAuF;;;;;;;;;;;;sCAMrH,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;yFAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;kDAGpC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3F;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Hero.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\n\nconst Hero = () => {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden\">\n      {/* Background decorations */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.5 }}\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\"\n          >\n            <Sparkles className=\"w-4 h-4 mr-2\" />\n            全新AI技术，引领未来\n          </motion.div>\n\n          {/* Main heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight\"\n          >\n            智能AI助手\n            <br />\n            <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              重新定义工作方式\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n            className=\"text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            利用最先进的人工智能技术，提升工作效率，释放创造力。\n            让AI成为您最得力的工作伙伴。\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5, duration: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n            >\n              立即开始\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"px-8 py-4 text-lg font-semibold border-2 hover:bg-gray-50\"\n            >\n              观看演示\n            </Button>\n          </motion.div>\n\n          {/* Features */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.8 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 max-w-4xl mx-auto\"\n          >\n            <div className=\"flex items-center justify-center space-x-3 text-gray-700\">\n              <Zap className=\"h-6 w-6 text-yellow-500\" />\n              <span className=\"font-medium\">极速响应</span>\n            </div>\n            <div className=\"flex items-center justify-center space-x-3 text-gray-700\">\n              <Shield className=\"h-6 w-6 text-green-500\" />\n              <span className=\"font-medium\">安全可靠</span>\n            </div>\n            <div className=\"flex items-center justify-center space-x-3 text-gray-700\">\n              <Sparkles className=\"h-6 w-6 text-purple-500\" />\n              <span className=\"font-medium\">智能学习</span>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAKvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;gCACX;8CAEC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAM/F,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;;wCACX;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;KApGM;uCAsGS", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Features.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { \n  Brain, \n  MessageSquare, \n  Image, \n  FileText, \n  BarChart3, \n  Zap,\n  Shield,\n  Globe,\n  Users\n} from \"lucide-react\"\n\nconst Features = () => {\n  const features = [\n    {\n      icon: Brain,\n      title: \"智能对话\",\n      description: \"基于大语言模型的智能对话系统，理解上下文，提供精准回答\",\n      color: \"text-blue-600\",\n      bgColor: \"bg-blue-100\"\n    },\n    {\n      icon: Image,\n      title: \"图像生成\",\n      description: \"AI驱动的图像创作工具，从文字描述生成高质量图像\",\n      color: \"text-purple-600\",\n      bgColor: \"bg-purple-100\"\n    },\n    {\n      icon: FileText,\n      title: \"文档处理\",\n      description: \"智能文档分析、总结和生成，提升文档处理效率\",\n      color: \"text-green-600\",\n      bgColor: \"bg-green-100\"\n    },\n    {\n      icon: BarChart3,\n      title: \"数据分析\",\n      description: \"AI辅助数据分析，快速洞察数据背后的价值\",\n      color: \"text-orange-600\",\n      bgColor: \"bg-orange-100\"\n    },\n    {\n      icon: MessageSquare,\n      title: \"多语言支持\",\n      description: \"支持多种语言的实时翻译和跨语言交流\",\n      color: \"text-pink-600\",\n      bgColor: \"bg-pink-100\"\n    },\n    {\n      icon: Zap,\n      title: \"实时响应\",\n      description: \"毫秒级响应速度，流畅的用户体验\",\n      color: \"text-yellow-600\",\n      bgColor: \"bg-yellow-100\"\n    }\n  ]\n\n  const stats = [\n    { number: \"1M+\", label: \"活跃用户\" },\n    { number: \"99.9%\", label: \"服务可用性\" },\n    { number: \"50+\", label: \"支持语言\" },\n    { number: \"24/7\", label: \"技术支持\" }\n  ]\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            强大的AI功能\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            集成多种AI技术，为您提供全方位的智能解决方案\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group p-6 rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300\"\n            >\n              <div className={`inline-flex p-3 rounded-lg ${feature.bgColor} mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                <feature.icon className={`h-6 w-6 ${feature.color}`} />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12\"\n        >\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-white mb-2\">\n              值得信赖的AI平台\n            </h3>\n            <p className=\"text-blue-100\">\n              全球用户的共同选择\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center\"\n              >\n                <div className=\"text-3xl md:text-4xl font-bold text-white mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-blue-100 font-medium\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Features\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAeA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,iNAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAO,OAAO;QAAO;QAC/B;YAAE,QAAQ;YAAS,OAAO;QAAQ;QAClC;YAAE,QAAQ;YAAO,OAAO;QAAO;QAC/B;YAAE,QAAQ;YAAQ,OAAO;QAAO;KACjC;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAW,AAAC,8BAA6C,OAAhB,QAAQ,OAAO,EAAC;8CAC5D,cAAA,6LAAC,QAAQ,IAAI;wCAAC,WAAW,AAAC,WAAwB,OAAd,QAAQ,KAAK;;;;;;;;;;;8CAEnD,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAdjB,QAAQ,KAAK;;;;;;;;;;8BAqBxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAXR,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB/B;KAxIM;uCA0IS", "debugId": null}}]}