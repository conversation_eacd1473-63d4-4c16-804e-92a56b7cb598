{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Features.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Features.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Features.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Features.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Features.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Features.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Brain, Sparkles, Mail, Phone, MapPin } from \"lucide-react\"\n\nconst Footer = () => {\n  const footerLinks = {\n    product: [\n      { name: \"AI对话\", href: \"/chat\" },\n      { name: \"图像生成\", href: \"/image\" },\n      { name: \"文档处理\", href: \"/document\" },\n      { name: \"数据分析\", href: \"/analytics\" }\n    ],\n    company: [\n      { name: \"关于我们\", href: \"/about\" },\n      { name: \"新闻动态\", href: \"/news\" },\n      { name: \"职业机会\", href: \"/careers\" },\n      { name: \"联系我们\", href: \"/contact\" }\n    ],\n    support: [\n      { name: \"帮助中心\", href: \"/help\" },\n      { name: \"API文档\", href: \"/docs\" },\n      { name: \"开发者\", href: \"/developers\" },\n      { name: \"社区\", href: \"/community\" }\n    ],\n    legal: [\n      { name: \"隐私政策\", href: \"/privacy\" },\n      { name: \"服务条款\", href: \"/terms\" },\n      { name: \"<PERSON><PERSON>政策\", href: \"/cookies\" },\n      { name: \"安全\", href: \"/security\" }\n    ]\n  }\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"relative\">\n                <Brain className=\"h-8 w-8 text-blue-400\" />\n                <Sparkles className=\"h-4 w-4 text-purple-400 absolute -top-1 -right-1\" />\n              </div>\n              <span className=\"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">\n                AI智能\n              </span>\n            </Link>\n            <p className=\"text-gray-400 mb-6 leading-relaxed\">\n              致力于为全球用户提供最先进的AI技术解决方案，\n              让人工智能真正服务于人类的创造力和生产力。\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <Mail className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <Phone className=\"h-4 w-4\" />\n                <span>+86 ************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <MapPin className=\"h-4 w-4\" />\n                <span>北京市朝阳区科技园区</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">产品</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">公司</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">支持</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">法律</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © 2024 AI智能. 保留所有权利.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              隐私政策\n            </Link>\n            <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              服务条款\n            </Link>\n            <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              网站地图\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAY;YAClC;gBAAE,MAAM;gBAAQ,MAAM;YAAa;SACpC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAQ,MAAM;YAAW;SAClC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAS,MAAM;YAAQ;YAC/B;gBAAE,MAAM;gBAAO,MAAM;YAAc;YACnC;gBAAE,MAAM;gBAAM,MAAM;YAAa;SAClC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAY,MAAM;YAAW;YACrC;gBAAE,MAAM;gBAAM,MAAM;YAAY;SACjC;IACH;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC;4CAAK,WAAU;sDAA+F;;;;;;;;;;;;8CAIjH,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwE;;;;;;8CAGxG,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAwE;;;;;;8CAGtG,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpH;uCAEe", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Ai%E7%BD%91%E7%AB%99/ai-website/src/app/page.tsx"], "sourcesContent": ["import Header from \"@/components/Header\"\nimport Hero from \"@/components/Hero\"\nimport Features from \"@/components/Features\"\nimport Footer from \"@/components/Footer\"\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main>\n        <Hero />\n        <Features />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,0HAAA,CAAA,UAAI;;;;;kCACL,8OAAC,8HAAA,CAAA,UAAQ;;;;;;;;;;;0BAEX,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}